"use client";

import Link from "next/link";
import Image from "next/image";
import { NewspaperIcon } from "@heroicons/react/24/outline";

interface FanPage {
  id: string;
  name: string;
  description: string | null;
  category: string | null;
  profileImage: string | null;
  coverImage: string | null;
  createdAt: string;
}

interface FanPageCardProps {
  page: FanPage;
}

export function FanPageCard({ page }: FanPageCardProps) {
  return (
    <Link href={`/pages/${page.id}`}>
      <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 overflow-hidden">
        {/* Cover Image */}
        <div className="h-24 bg-gradient-to-r from-purple-500 to-pink-600 relative">
          {page.coverImage ? (
            <Image
              src={page.coverImage}
              alt={page.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600" />
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start space-x-3">
            {/* Profile Image */}
            <div className="flex-shrink-0 -mt-8 relative z-10">
              {page.profileImage ? (
                <Image
                  src={page.profileImage}
                  alt={page.name}
                  width={48}
                  height={48}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white"
                />
              ) : (
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center border-2 border-white">
                  <NewspaperIcon className="w-6 h-6 text-gray-500" />
                </div>
              )}
            </div>

            {/* Page Info */}
            <div className="flex-1 min-w-0 pt-2">
              <h3 className="text-sm font-semibold text-gray-900 line-clamp-1">
                {page.name}
              </h3>
              
              {page.description && (
                <p className="text-xs text-gray-600 line-clamp-2 mt-1">
                  {page.description}
                </p>
              )}

              {page.category && (
                <span className="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mt-2">
                  {page.category}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
