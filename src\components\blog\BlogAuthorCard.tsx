"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { 
  UserPlusIcon, 
  CheckIcon,
  MapPinIcon,
  LinkIcon,
  CalendarIcon
} from "@heroicons/react/24/outline";

interface BlogAuthorCardProps {
  author: {
    id: string;
    name: string;
    username?: string | null;
    image?: string | null;
    bio?: string | null;
    location?: string | null;
    website?: string | null;
    joinedAt?: string | null;
    followersCount?: number;
    followingCount?: number;
    postsCount?: number;
    isFollowing?: boolean;
  };
  variant?: "compact" | "full";
  showFollowButton?: boolean;
  onFollow?: (authorId: string) => void;
}

export function BlogAuthorCard({ 
  author, 
  variant = "full",
  showFollowButton = true,
  onFollow 
}: BlogAuthorCardProps) {
  const [isFollowing, setIsFollowing] = useState(author.isFollowing || false);
  const [followersCount, setFollowersCount] = useState(author.followersCount || 0);

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
    setFollowersCount(prev => isFollowing ? prev - 1 : prev + 1);
    onFollow?.(author.id);
  };

  if (variant === "compact") {
    return (
      <div className="flex items-center space-x-3 p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
        <Link href={`/profile/${author.username || author.id}`}>
          <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
            {author.image ? (
              <Image
                src={author.image}
                alt={author.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium">
                {author.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </Link>
        
        <div className="flex-1 min-w-0">
          <Link href={`/profile/${author.username || author.id}`}>
            <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors">
              {author.name}
            </h3>
          </Link>
          {author.username && (
            <p className="text-sm text-gray-500">@{author.username}</p>
          )}
          {author.bio && (
            <p className="text-sm text-gray-600 line-clamp-1 mt-1">{author.bio}</p>
          )}
        </div>
        
        {showFollowButton && (
          <Button
            variant={isFollowing ? "outline" : "primary"}
            size="sm"
            onClick={handleFollow}
          >
            {isFollowing ? (
              <>
                <CheckIcon className="h-4 w-4 mr-1" />
                Following
              </>
            ) : (
              <>
                <UserPlusIcon className="h-4 w-4 mr-1" />
                Follow
              </>
            )}
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start space-x-4 mb-4">
        <Link href={`/profile/${author.username || author.id}`}>
          <div className="relative w-16 h-16 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
            {author.image ? (
              <Image
                src={author.image}
                alt={author.name}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium text-xl">
                {author.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </Link>
        
        <div className="flex-1 min-w-0">
          <Link href={`/profile/${author.username || author.id}`}>
            <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
              {author.name}
            </h3>
          </Link>
          {author.username && (
            <p className="text-gray-500">@{author.username}</p>
          )}
          
          {/* Stats */}
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
            <span>{followersCount} followers</span>
            {author.followingCount !== undefined && (
              <span>{author.followingCount} following</span>
            )}
            {author.postsCount !== undefined && (
              <span>{author.postsCount} posts</span>
            )}
          </div>
        </div>
        
        {showFollowButton && (
          <Button
            variant={isFollowing ? "outline" : "primary"}
            size="sm"
            onClick={handleFollow}
          >
            {isFollowing ? (
              <>
                <CheckIcon className="h-4 w-4 mr-1" />
                Following
              </>
            ) : (
              <>
                <UserPlusIcon className="h-4 w-4 mr-1" />
                Follow
              </>
            )}
          </Button>
        )}
      </div>
      
      {/* Bio */}
      {author.bio && (
        <p className="text-gray-700 mb-4 leading-relaxed">{author.bio}</p>
      )}
      
      {/* Additional Info */}
      <div className="space-y-2 text-sm text-gray-600">
        {author.location && (
          <div className="flex items-center">
            <MapPinIcon className="h-4 w-4 mr-2" />
            <span>{author.location}</span>
          </div>
        )}
        
        {author.website && (
          <div className="flex items-center">
            <LinkIcon className="h-4 w-4 mr-2" />
            <a 
              href={author.website} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-700 hover:underline"
            >
              {author.website.replace(/^https?:\/\//, '')}
            </a>
          </div>
        )}
        
        {author.joinedAt && (
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>Joined {new Date(author.joinedAt).toLocaleDateString('en-US', { 
              month: 'long', 
              year: 'numeric' 
            })}</span>
          </div>
        )}
      </div>
    </div>
  );
}
