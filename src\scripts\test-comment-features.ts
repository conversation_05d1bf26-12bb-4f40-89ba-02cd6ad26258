import { db } from "@/lib/db";
import { posts, comments, likes, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { eq, and } from "drizzle-orm";

/**
 * Test script for comment reply and like/dislike features
 * This script creates test data and verifies the functionality
 */

async function createTestData() {
  console.log("🚀 Creating test data for comment features...");

  try {
    // Get or create a test user
    let testUser = await db.query.users.findFirst({
      where: eq(users.email, "<EMAIL>"),
    });

    if (!testUser) {
      const userId = uuidv4();
      await db.insert(users).values({
        id: userId,
        name: "Test User",
        email: "<EMAIL>",
        username: "testuser",
        role: "user",
        isAdmin: false,
        status: "active",
        isActive: true,
      });

      testUser = await db.query.users.findFirst({
        where: eq(users.email, "<EMAIL>"),
      });
    }

    if (!testUser) {
      throw new Error("Failed to create test user");
    }

    // Create a test post
    const postId = uuidv4();
    await db.insert(posts).values({
      id: postId,
      userId: testUser.id,
      content: "This is a test post for comment features",
      privacy: "public",
      moderationStatus: "approved",
    });

    console.log("✅ Created test post:", postId);

    // Create parent comments
    const parentComment1Id = uuidv4();
    const parentComment2Id = uuidv4();

    await db.insert(comments).values([
      {
        id: parentComment1Id,
        content: "This is the first parent comment",
        userId: testUser.id,
        postId: postId,
        parentId: null,
      },
      {
        id: parentComment2Id,
        content: "This is the second parent comment",
        userId: testUser.id,
        postId: postId,
        parentId: null,
      },
    ]);

    console.log("✅ Created parent comments");

    // Create reply comments
    const replyComment1Id = uuidv4();
    const replyComment2Id = uuidv4();

    await db.insert(comments).values([
      {
        id: replyComment1Id,
        content: "This is a reply to the first comment",
        userId: testUser.id,
        postId: postId,
        parentId: parentComment1Id,
      },
      {
        id: replyComment2Id,
        content: "This is another reply to the first comment",
        userId: testUser.id,
        postId: postId,
        parentId: parentComment1Id,
      },
    ]);

    console.log("✅ Created reply comments");

    // Create likes and dislikes for comments
    await db.insert(likes).values([
      {
        id: uuidv4(),
        userId: testUser.id,
        postId: null,
        commentId: parentComment1Id,
        type: "like",
      },
      {
        id: uuidv4(),
        userId: testUser.id,
        postId: null,
        commentId: parentComment2Id,
        type: "dislike",
      },
      {
        id: uuidv4(),
        userId: testUser.id,
        postId: null,
        commentId: replyComment1Id,
        type: "like",
      },
    ]);

    console.log("✅ Created likes and dislikes for comments");

    return {
      userId: testUser.id,
      postId,
      parentComment1Id,
      parentComment2Id,
      replyComment1Id,
      replyComment2Id,
    };
  } catch (error) {
    console.error("❌ Error creating test data:", error);
    throw error;
  }
}

async function testCommentRetrieval(postId: string) {
  console.log("🔍 Testing comment retrieval...");

  try {
    // Fetch comments with likes/dislikes
    const postComments = await db.query.comments.findMany({
      where: eq(comments.postId, postId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
        likes: true,
      },
      orderBy: (comments, { desc }) => [desc(comments.createdAt)],
    });

    console.log("📊 Retrieved comments:", postComments.length);

    // Process comments to show structure
    postComments.forEach((comment) => {
      const likeCount = comment.likes.filter(like => like.type === 'like').length;
      const dislikeCount = comment.likes.filter(like => like.type === 'dislike').length;
      
      console.log(`💬 Comment: ${comment.content.substring(0, 50)}...`);
      console.log(`   👍 Likes: ${likeCount}, 👎 Dislikes: ${dislikeCount}`);
      console.log(`   🔗 Parent: ${comment.parentId ? 'Reply' : 'Parent'}`);
      console.log("---");
    });

    return postComments;
  } catch (error) {
    console.error("❌ Error testing comment retrieval:", error);
    throw error;
  }
}

async function cleanupTestData(testData: any) {
  console.log("🧹 Cleaning up test data...");

  try {
    // Delete likes
    await db.delete(likes).where(eq(likes.userId, testData.userId));
    
    // Delete comments
    await db.delete(comments).where(eq(comments.postId, testData.postId));
    
    // Delete post
    await db.delete(posts).where(eq(posts.id, testData.postId));
    
    // Delete test user
    await db.delete(users).where(eq(users.id, testData.userId));

    console.log("✅ Cleanup completed");
  } catch (error) {
    console.error("❌ Error during cleanup:", error);
  }
}

async function main() {
  console.log("🎯 Starting comment features test...\n");

  try {
    // Create test data
    const testData = await createTestData();
    console.log("\n");

    // Test comment retrieval
    await testCommentRetrieval(testData.postId);
    console.log("\n");

    // Cleanup
    await cleanupTestData(testData);

    console.log("🎉 All tests completed successfully!");
  } catch (error) {
    console.error("💥 Test failed:", error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

export { createTestData, testCommentRetrieval, cleanupTestData };
