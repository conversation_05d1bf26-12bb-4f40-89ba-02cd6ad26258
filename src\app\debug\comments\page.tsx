"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { CommentDebug } from "@/components/debug/CommentDebug";

export default function CommentDebugPage() {
  const { data: session, status } = useSession();
  const [posts, setPosts] = useState<any[]>([]);
  const [selectedPostId, setSelectedPostId] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const fetchPosts = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/posts");
      if (response.ok) {
        const data = await response.json();
        setPosts(data.posts || []);
        if (data.posts && data.posts.length > 0) {
          setSelectedPostId(data.posts[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session) {
      fetchPosts();
    }
  }, [session]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Comment Debug Tool</h1>
          <p className="text-gray-600">Please login to use this tool</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Comment Debug Tool</h1>
          <p className="text-gray-600 mb-4">
            This tool helps debug comment reply and like/dislike functionality.
          </p>
          
          {/* Post Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select a Post to Test Comments:
            </label>
            {loading ? (
              <div className="text-gray-500">Loading posts...</div>
            ) : posts.length === 0 ? (
              <div className="text-gray-500">No posts found. Create a post first.</div>
            ) : (
              <select
                value={selectedPostId}
                onChange={(e) => setSelectedPostId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {posts.map((post) => (
                  <option key={post.id} value={post.id}>
                    {post.content?.substring(0, 100) || "Untitled Post"}...
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">How to Test:</h3>
            <ol className="list-decimal list-inside text-sm text-blue-700 space-y-1">
              <li>Select a post from the dropdown above</li>
              <li>Add a new comment using the form below</li>
              <li>Click "Reply" on any comment to add a reply</li>
              <li>Check the "Raw API Data" section to see the data structure</li>
              <li>Verify that replies appear nested under parent comments</li>
            </ol>
          </div>
        </div>

        {/* Comment Debug Component */}
        {selectedPostId && (
          <div className="bg-white rounded-lg shadow">
            <CommentDebug postId={selectedPostId} />
          </div>
        )}
      </div>
    </div>
  );
}
