import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

async function checkTables() {
  const connection = await mysql.createConnection({
    host: process.env.DATABASE_HOST,
    user: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
  });

  try {
    console.log('Connected to database. Checking tables...');
    
    // Get all tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('Tables in database:');
    console.log(tables);
    
    // Check if events table exists
    const [eventsTable] = await connection.execute('SHOW TABLES LIKE "events"');
    if (Array.isArray(eventsTable) && eventsTable.length > 0) {
      console.log('Events table exists');
      
      // Check structure of events table
      const [eventsColumns] = await connection.execute('DESCRIBE events');
      console.log('Events table structure:');
      console.log(eventsColumns);
      
      // Check if there are any records in the events table
      const [eventsCount] = await connection.execute('SELECT COUNT(*) as count FROM events');
      console.log('Number of records in events table:', eventsCount);
    } else {
      console.log('Events table does not exist');
    }
    
    // Check if event_attendees table exists
    const [attendeesTable] = await connection.execute('SHOW TABLES LIKE "event_attendees"');
    if (Array.isArray(attendeesTable) && attendeesTable.length > 0) {
      console.log('Event_attendees table exists');
      
      // Check structure of event_attendees table
      const [attendeesColumns] = await connection.execute('DESCRIBE event_attendees');
      console.log('Event_attendees table structure:');
      console.log(attendeesColumns);
      
      // Check if there are any records in the event_attendees table
      const [attendeesCount] = await connection.execute('SELECT COUNT(*) as count FROM event_attendees');
      console.log('Number of records in event_attendees table:', attendeesCount);
    } else {
      console.log('Event_attendees table does not exist');
    }
  } catch (error) {
    console.error('Error checking tables:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

checkTables().catch(console.error);
