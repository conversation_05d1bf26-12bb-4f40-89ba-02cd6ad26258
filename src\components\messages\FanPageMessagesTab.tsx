"use client";

import { useSession } from "next-auth/react";
import { useFanPageMessagesManagement } from "@/hooks/useFanPageMessagesManagement";
import { PageSelector } from "./PageSelector";

import { FanPageConversationList } from "./FanPageConversationList";
import { FanPageChatArea } from "./FanPageChatArea";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import {
  ArrowPathIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  PlusIcon
} from "@heroicons/react/24/outline";
import {
  StarIcon as StarIconSolid
} from "@heroicons/react/24/solid";
import { cn } from "@/lib/utils";

interface FanPageMessagesTabProps {
  className?: string;
}

export function FanPageMessagesTab({ className }: FanPageMessagesTabProps) {
  const { data: session } = useSession();
  const {
    ownedPages,
    selectedPageId,
    setSelectedPageId,
    conversations,
    selectedConversation,
    messages,
    stats,
    isLoading,
    isSending,
    sendMessage,
    sendReply,
    selectConversation,
    markAsRead,
    refreshData,
  } = useFanPageMessagesManagement();

  const selectedUser = conversations.find(c => c.senderId === selectedConversation)?.sender;

  const handleMarkAsRead = async (conversationId: string) => {
    const conversation = conversations.find(c => c.senderId === conversationId);
    if (conversation) {
      const unreadMessageIds = conversation.messages
        .filter(m => !m.read)
        .map(m => m.id);

      if (unreadMessageIds.length > 0) {
        await markAsRead(unreadMessageIds);
      }
    }
  };

  if (!session?.user) {
    return (
      <div className={cn("flex items-center justify-center min-h-[600px]", className)}>
        <div className="text-center max-w-md mx-auto p-8">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-xl opacity-20 animate-pulse"></div>
            <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-4 w-20 h-20 mx-auto mb-6">
              <ExclamationTriangleIcon className="h-12 w-12 text-white" />
            </div>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Authentication Required</h3>
          <p className="text-gray-600 mb-6">
            Please log in to access your fan page messaging dashboard
          </p>
          <Button
            onClick={() => window.location.href = "/auth/signin"}
            className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105"
          >
            Sign In to Continue
          </Button>
        </div>
      </div>
    );
  }

  if (ownedPages.length === 0 && !isLoading) {
    return (
      <div className={cn("flex items-center justify-center min-h-[600px]", className)}>
        <div className="text-center max-w-lg mx-auto p-8">
          <div className="relative mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-xl opacity-20 animate-pulse"></div>
            <div className="relative bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-6 w-24 h-24 mx-auto mb-6">
              <StarIconSolid className="h-12 w-12 text-white" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">Create Your First Fan Page</h3>
          <p className="text-gray-600 mb-2">
            Start building your community and connect with your audience
          </p>
          <p className="text-sm text-gray-500 mb-8">
            You need to own a fan page to manage messages and engage with your followers
          </p>
          <div className="space-y-4">
            <Button
              onClick={() => window.location.href = "/fan-pages/create"}
              className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-8 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Fan Page
            </Button>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center">
                <UserGroupIcon className="h-4 w-4 mr-1" />
                <span>Build Community</span>
              </div>
              <div className="flex items-center">
                <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
                <span>Engage Fans</span>
              </div>
              <div className="flex items-center">
                <SparklesIcon className="h-4 w-4 mr-1" />
                <span>Grow Brand</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-8", className)}>
      {/* Header Section */}

      {/* Messages interface */}
      {selectedPageId && (
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          {/* Interface Header */}
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Page Selector as Main Element */}
                <div className="flex-1 max-w-sm">
                  <PageSelector
                    pages={ownedPages}
                    selectedPageId={selectedPageId}
                    onPageSelect={setSelectedPageId}
                    variant="compact"
                  />
                </div>
                {/* Unread Messages Badge */}
                {stats.unreadMessages > 0 && (
                  <div className="flex items-center">
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 text-red-800">
                      {stats.unreadMessages} unread
                    </span>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  onClick={refreshData}
                  variant="outline"
                  size="sm"
                  disabled={isLoading}
                  className="bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white hover:shadow-md transition-all duration-200"
                >
                  {isLoading ? (
                    <Spinner className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowPathIcon className="h-4 w-4 mr-1" />
                  )}
                  Refresh
                </Button>
              </div>
            </div>
          </div>

          {/* Main Interface */}
          <div className="flex h-[calc(100vh-450px)] min-h-[500px]">
            {/* Conversation list */}
            <div className="w-80 border-r border-gray-200 bg-gray-50/50">
              <FanPageConversationList
                conversations={conversations}
                selectedConversation={selectedConversation}
                onConversationSelect={selectConversation}
                onMarkAsRead={handleMarkAsRead}
                isLoading={isLoading}
              />
            </div>

            {/* Chat area */}
            <div className="flex-1 bg-white">
              <FanPageChatArea
                messages={messages}
                selectedUser={selectedUser || null}
                currentUserId={session.user.id}
                onSendMessage={sendMessage}
                onSendReply={sendReply}
                isSending={isSending}
              />
            </div>
          </div>
        </div>
      )}

      {/* Loading state */}
      {isLoading && !selectedPageId && (
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-xl opacity-20 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-6 w-20 h-20 mx-auto mb-4">
                <Spinner className="h-8 w-8 text-white" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Your Fan Pages</h3>
            <p className="text-sm text-gray-500">Please wait while we fetch your pages...</p>
            <div className="mt-4 flex items-center justify-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
