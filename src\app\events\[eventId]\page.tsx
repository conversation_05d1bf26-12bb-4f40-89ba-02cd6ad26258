"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { EventComments } from "@/components/events/EventComments";
import { InviteFriendsModal } from "@/components/events/InviteFriendsModal";
import { formatDate } from "@/lib/utils";
import {
  CalendarIcon,
  MapPinIcon,
  UserGroupIcon,
  GlobeAltIcon,
  LockClosedIcon,
  UsersIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ChevronLeftIcon,
} from "@heroicons/react/24/outline";

export default function EventDetailPage() {
  // Get the eventId from the URL params
  const params = useParams();
  const eventId = params?.eventId as string;
  const router = useRouter();
  const { data: session } = useSession();

  const [event, setEvent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRsvpLoading, setIsRsvpLoading] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchEvent = async () => {
      if (!eventId) {
        setError("Event ID is missing");
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        console.log(`Fetching event with ID: ${eventId}`);
        const response = await fetch(`/api/events/${eventId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error("Event not found");
          }

          // Try to get more detailed error information
          let errorMessage = "Failed to fetch event";
          try {
            const errorData = await response.json();
            console.log("Error response data:", errorData);
            errorMessage = errorData.message || errorData.details || errorMessage;
          } catch (parseError) {
            console.error("Error parsing error response:", parseError);
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log("Event data received:", data);
        setEvent(data);
      } catch (err) {
        console.error("Error fetching event:", err);
        setError(err instanceof Error ? err.message : "Failed to load event");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvent();
  }, [eventId]);

  const handleRsvp = async (status: "going" | "interested" | "not_going") => {
    if (!session?.user || !eventId) return;

    setIsRsvpLoading(true);

    try {
      const response = await fetch(`/api/events/${eventId}/attendees`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update RSVP");
      }

      // Update the local state
      setEvent((prev: any) => {
        if (!prev) return prev;

        return {
          ...prev,
          userAttendance: status,
          attendeeCounts: {
            ...prev.attendeeCounts,
            [status]: prev.attendeeCounts[status] + 1,
            ...(prev.userAttendance && {
              [prev.userAttendance]: Math.max(0, prev.attendeeCounts[prev.userAttendance] - 1)
            })
          }
        };
      });
    } catch (err) {
      console.error("Error updating RSVP:", err);
      alert(err instanceof Error ? err.message : "Failed to update RSVP");
    } finally {
      setIsRsvpLoading(false);
    }
  };

  const handleDeleteEvent = async () => {
    if (!session?.user || !event || !eventId || event.host.id !== session.user.id) return;

    if (!window.confirm("Are you sure you want to delete this event? This action cannot be undone.")) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/events/${eventId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to delete event");
      }

      router.push("/events");
      router.refresh();
    } catch (err) {
      console.error("Error deleting event:", err);
      alert(err instanceof Error ? err.message : "Failed to delete event. Please try again.");
    } finally {
      setIsDeleting(false);
    }
  };

  const getVisibilityIcon = () => {
    if (!event) return null;

    switch (event.visibility) {
      case "public":
        return <GlobeAltIcon className="h-5 w-5 text-gray-500" />;
      case "private":
        return <LockClosedIcon className="h-5 w-5 text-gray-500" />;
      case "friends":
        return <UsersIcon className="h-5 w-5 text-gray-500" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="flex justify-center py-20">
            <Spinner size="lg" />
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error || !event) {
    return (
      <MainLayout>
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="rounded-lg bg-red-50 p-6 text-red-800">
            <h2 className="text-lg font-semibold">Error</h2>
            <p className="mt-2">{error || "Failed to load event"}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => router.push("/events")}
            >
              <ChevronLeftIcon className="h-5 w-5 mr-1" />
              Back to Events
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  const startDate = new Date(event.startTime);
  const endDate = new Date(event.endTime);
  const formattedStartDate = formatDate(startDate);
  const formattedStartTime = startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  const formattedEndDate = formatDate(endDate);
  const formattedEndTime = endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  const isSameDay = formattedStartDate === formattedEndDate;

  const isHost = session?.user?.id === event.host.id;

  return (
    <MainLayout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => router.push("/events")}
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Back to Events
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          {/* Cover Image */}
          <div className="relative h-64 w-full bg-gradient-to-r from-blue-500 to-purple-600">
            {event.coverImage && (
              <Image
                src={event.coverImage}
                alt={event.name}
                fill
                className="object-cover"
              />
            )}
          </div>

          {/* Event Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">{event.name}</h1>
              <div className="flex items-center">
                {getVisibilityIcon()}
                <span className="ml-1 text-sm text-gray-500 capitalize">{event.visibility}</span>
              </div>
            </div>

            <div className="mt-4 flex flex-wrap gap-4">
              <div className="flex items-center text-gray-600">
                <CalendarIcon className="h-5 w-5 mr-2" />
                <div>
                  <div>{formattedStartDate} at {formattedStartTime}</div>
                  {!isSameDay && (
                    <div className="text-sm text-gray-500">
                      to {formattedEndDate} at {formattedEndTime}
                    </div>
                  )}
                  {isSameDay && (
                    <div className="text-sm text-gray-500">
                      to {formattedEndTime}
                    </div>
                  )}
                </div>
              </div>

              {(event.location || event.isOnline) && (
                <div className="flex items-center text-gray-600">
                  {event.location ? (
                    <>
                      <MapPinIcon className="h-5 w-5 mr-2" />
                      <span>{event.location}</span>
                    </>
                  ) : event.isOnline ? (
                    <>
                      <GlobeAltIcon className="h-5 w-5 mr-2" />
                      <span>
                        Online Event
                        {event.onlineLink && (
                          <a
                            href={event.onlineLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 text-blue-600 hover:underline"
                          >
                            Join
                          </a>
                        )}
                      </span>
                    </>
                  ) : null}
                </div>
              )}

              <div className="flex items-center text-gray-600">
                <UserGroupIcon className="h-5 w-5 mr-2" />
                <div>
                  <span>Hosted by </span>
                  <Link
                    href={`/profile/${event.host.username || event.host.id}`}
                    className="text-blue-600 hover:underline"
                  >
                    {event.host.name}
                  </Link>
                </div>
              </div>
            </div>

            <div className="mt-6 flex flex-wrap gap-2">
              {isHost ? (
                <>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/events/${eventId}/edit`)}
                  >
                    <PencilIcon className="h-5 w-5 mr-1" />
                    Edit Event
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setIsInviteModalOpen(true)}
                  >
                    <UserGroupIcon className="h-5 w-5 mr-1" />
                    Invite Friends
                  </Button>
                  <Button
                    variant="danger"
                    onClick={handleDeleteEvent}
                    disabled={isDeleting}
                  >
                    <TrashIcon className="h-5 w-5 mr-1" />
                    {isDeleting ? "Deleting..." : "Delete Event"}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant={event.userAttendance === "going" ? "primary" : "outline"}
                    onClick={() => handleRsvp("going")}
                    disabled={isRsvpLoading}
                  >
                    Going ({event.attendeeCounts.going})
                  </Button>
                  <Button
                    variant={event.userAttendance === "interested" ? "primary" : "outline"}
                    onClick={() => handleRsvp("interested")}
                    disabled={isRsvpLoading}
                  >
                    Interested ({event.attendeeCounts.interested})
                  </Button>
                  <Button
                    variant={event.userAttendance === "not_going" ? "primary" : "outline"}
                    onClick={() => handleRsvp("not_going")}
                    disabled={isRsvpLoading}
                  >
                    Not Going
                  </Button>
                </>
              )}
              <Button variant="outline">
                <ShareIcon className="h-5 w-5 mr-1" />
                Share
              </Button>
            </div>
          </div>

          {/* Event Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="md:col-span-2">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Details</h2>
                {event.description ? (
                  <div className="prose max-w-none">
                    <p className="whitespace-pre-wrap">{event.description}</p>
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No description provided</p>
                )}

                <div className="mt-8">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Discussion</h2>
                  <EventComments eventId={eventId} />
                </div>
              </div>

              <div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Event Information</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium text-gray-500">Date and Time</div>
                      <div className="mt-1 flex items-center">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                        <div>
                          <div className="text-sm">{formattedStartDate} at {formattedStartTime}</div>
                          {!isSameDay && (
                            <div className="text-sm text-gray-500">
                              to {formattedEndDate} at {formattedEndTime}
                            </div>
                          )}
                          {isSameDay && (
                            <div className="text-sm text-gray-500">
                              to {formattedEndTime}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {(event.location || event.isOnline) && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Location</div>
                        <div className="mt-1 flex items-center">
                          {event.location ? (
                            <>
                              <MapPinIcon className="h-5 w-5 text-gray-400 mr-2" />
                              <div className="text-sm">{event.location}</div>
                            </>
                          ) : event.isOnline ? (
                            <>
                              <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-2" />
                              <div className="text-sm">
                                Online Event
                                {event.onlineLink && (
                                  <a
                                    href={event.onlineLink}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="ml-2 text-blue-600 hover:underline"
                                  >
                                    Join
                                  </a>
                                )}
                              </div>
                            </>
                          ) : null}
                        </div>
                      </div>
                    )}

                    <div>
                      <div className="text-sm font-medium text-gray-500">Host</div>
                      <div className="mt-1 flex items-center">
                        <UserGroupIcon className="h-5 w-5 text-gray-400 mr-2" />
                        <Link
                          href={`/profile/${event.host.username || event.host.id}`}
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {event.host.name}
                        </Link>
                      </div>
                    </div>

                    {event.category && (
                      <div>
                        <div className="text-sm font-medium text-gray-500">Category</div>
                        <div className="mt-1 text-sm">{event.category}</div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-6 bg-gray-50 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Attendees</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Going</span>
                      <span className="text-sm font-medium">{event.attendeeCounts.going}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Interested</span>
                      <span className="text-sm font-medium">{event.attendeeCounts.interested}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Not Going</span>
                      <span className="text-sm font-medium">{event.attendeeCounts.not_going}</span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => router.push(`/events/${eventId}/attendees`)}
                    >
                      View All Attendees
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isInviteModalOpen && (
        <InviteFriendsModal
          eventId={eventId}
          isOpen={isInviteModalOpen}
          onClose={() => setIsInviteModalOpen(false)}
        />
      )}
    </MainLayout>
  );
}
