Stack trace:
Frame         Function      Args
0007FFFFA350  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9250) msys-2.0.dll+0x1FE8E
0007FFFFA350  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA628) msys-2.0.dll+0x67F9
0007FFFFA350  000210046832 (000210286019, 0007FFFFA208, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA350  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA350  000210068E24 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA630  00021006A225 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAF2AD0000 ntdll.dll
7FFAF1AB0000 KERNEL32.DLL
7FFAEFD60000 KERNELBASE.dll
7FFAE9D70000 apphelp.dll
7FFAF24A0000 USER32.dll
7FFAEFD00000 win32u.dll
7FFAF0B20000 GDI32.dll
7FFAF02B0000 gdi32full.dll
7FFAF03E0000 msvcp_win.dll
7FFAF0500000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAF0D10000 advapi32.dll
7FFAF29E0000 msvcrt.dll
7FFAF1150000 sechost.dll
7FFAEFD30000 bcrypt.dll
7FFAF0A00000 RPCRT4.dll
7FFAEF280000 CRYPTBASE.DLL
7FFAF0480000 bcryptPrimitives.dll
7FFAF0860000 IMM32.DLL
