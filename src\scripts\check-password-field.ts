import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkPasswordField() {
  console.log('Checking password field in users table...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
    });

    console.log(`Connected to database '${dbName}'`);
    
    // Check users table structure
    const [columns] = await connection.execute("DESCRIBE users");
    console.log("\nUsers table structure:");
    console.log(columns);
    
    // Check if any users have passwords set
    const [users] = await connection.execute(
      "SELECT id, name, email, password IS NOT NULL as has_password FROM users LIMIT 5"
    );
    console.log("\nSample users and password status:");
    console.log(users);

    // Close the connection
    await connection.end();
    console.log("\nCheck completed successfully!");

  } catch (error) {
    console.error("Check failed:", error);
    process.exit(1);
  }
}

// Run the check
checkPasswordField();
