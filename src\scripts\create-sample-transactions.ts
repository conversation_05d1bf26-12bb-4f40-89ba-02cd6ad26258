import { db } from '../lib/db';
import {
  users,
  wallets,
  walletTransactions,
  paymentGateways,
  agents
} from '../lib/db/schema';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

async function createSampleTransactions() {
  try {
    console.log('🚀 Creating sample transactions...');

    // Create sample users if they don't exist
    const sampleUsers = [
      {
        id: uuidv4(),
        name: '<PERSON>',
        username: 'joh<PERSON><PERSON>',
        email: '<EMAIL>',
        image: null,
      },
      {
        id: uuidv4(),
        name: '<PERSON>',
        username: 'jane<PERSON>',
        email: '<EMAIL>',
        image: null,
      },
      {
        id: uuidv4(),
        name: '<PERSON>',
        username: 'bob<PERSON><PERSON>',
        email: '<EMAIL>',
        image: null,
      }
    ];

    // Insert users
    for (const user of sampleUsers) {
      try {
        await db.insert(users).values(user);
        console.log(`✅ Created user: ${user.name}`);
      } catch (error) {
        console.log(`ℹ️ User ${user.name} already exists`);
      }
    }

    // Create wallets for users
    for (const user of sampleUsers) {
      try {
        await db.insert(wallets).values({
          id: uuidv4(),
          userId: user.id,
          generalBalance: '1000.00',
          earningBalance: '500.00',
          totalDeposited: '2000.00',
          totalWithdrawn: '500.00',
          totalSent: '300.00',
          totalReceived: '800.00',
          isActive: true,
        });
        console.log(`✅ Created wallet for: ${user.name}`);
      } catch (error) {
        console.log(`ℹ️ Wallet for ${user.name} already exists`);
      }
    }

    // Create sample payment gateways
    const sampleGateways = [
      {
        id: uuidv4(),
        name: 'stripe',
        displayName: 'Stripe',
        type: 'stripe' as const,
        isActive: true,
        config: { apiKey: 'sk_test_...', publishableKey: 'pk_test_...' },
        depositFee: '2.50',
        depositFixedFee: '0.30',
        minDeposit: '10.00',
        maxDeposit: '10000.00',
        currency: 'USD',
        sortOrder: 1,
      },
      {
        id: uuidv4(),
        name: 'paypal',
        displayName: 'PayPal',
        type: 'paypal' as const,
        isActive: true,
        config: { clientId: 'client_id', clientSecret: 'client_secret' },
        depositFee: '3.00',
        depositFixedFee: '0.50',
        minDeposit: '5.00',
        maxDeposit: '5000.00',
        currency: 'USD',
        sortOrder: 2,
      },
      {
        id: uuidv4(),
        name: 'manual',
        displayName: 'Manual Payment',
        type: 'manual' as const,
        isActive: true,
        config: { instructions: 'Please contact admin for manual payment' },
        depositFee: '0.00',
        depositFixedFee: '0.00',
        minDeposit: '1.00',
        maxDeposit: '50000.00',
        currency: 'USD',
        sortOrder: 3,
      }
    ];

    // Insert payment gateways
    for (const gateway of sampleGateways) {
      try {
        await db.insert(paymentGateways).values(gateway);
        console.log(`✅ Created payment gateway: ${gateway.displayName}`);
      } catch (error) {
        console.log(`ℹ️ Payment gateway ${gateway.displayName} already exists`);
      }
    }

    // Create sample agent
    const sampleAgent = {
      id: uuidv4(),
      userId: sampleUsers[0].id, // Use first sample user
      name: 'Cash Agent 1',
      serviceType: 'cash_pickup',
      phone: '+**********',
      email: '<EMAIL>',
      location: '123 Main St, City',
      accountNumber: 'ACC123456789',
      accountName: 'Cash Agent 1',
      dailyLimit: '10000.00',
      currentDailyAmount: '2500.00',
      commission: '2.00',
      isActive: true,
      isVerified: true,
      rating: '4.50',
      totalTransactions: 150,
      totalAmount: '75000.00',
    };

    try {
      await db.insert(agents).values(sampleAgent);
      console.log(`✅ Created agent: ${sampleAgent.name}`);
    } catch (error) {
      console.log(`ℹ️ Agent ${sampleAgent.name} already exists`);
    }

    // Create sample transactions
    const sampleTransactions = [
      // Deposits
      {
        id: uuidv4(),
        userId: sampleUsers[0].id,
        type: 'deposit' as const,
        amount: '100.00',
        fee: '2.80',
        netAmount: '97.20',
        walletType: 'general' as const,
        status: 'completed' as const,
        paymentGateway: 'stripe',
        gatewayTransactionId: 'pi_**********',
        reference: 'DEP-001',
        note: 'First deposit via Stripe',
        metadata: { gateway_fee: '2.80', currency: 'USD' },
      },
      {
        id: uuidv4(),
        userId: sampleUsers[1].id,
        type: 'deposit' as const,
        amount: '250.00',
        fee: '7.50',
        netAmount: '242.50',
        walletType: 'general' as const,
        status: 'pending' as const,
        paymentGateway: 'paypal',
        gatewayTransactionId: 'PAYID-123456',
        reference: 'DEP-002',
        note: 'PayPal deposit pending verification',
        metadata: { gateway_fee: '7.50', currency: 'USD' },
      },
      {
        id: uuidv4(),
        userId: sampleUsers[2].id,
        type: 'deposit' as const,
        amount: '500.00',
        fee: '0.00',
        netAmount: '500.00',
        walletType: 'general' as const,
        status: 'processing' as const,
        paymentGateway: 'manual',
        reference: 'DEP-003',
        note: 'Manual bank transfer',
        metadata: { bank_reference: 'TXN789123', currency: 'USD' },
      },
      // Send money transactions
      {
        id: uuidv4(),
        userId: sampleUsers[0].id,
        type: 'send' as const,
        amount: '50.00',
        fee: '1.00',
        netAmount: '49.00',
        walletType: 'general' as const,
        toUserId: sampleUsers[1].id,
        status: 'completed' as const,
        reference: 'SEND-001',
        note: 'Payment for lunch',
        metadata: { transfer_type: 'p2p' },
      },
      {
        id: uuidv4(),
        userId: sampleUsers[1].id,
        type: 'receive' as const,
        amount: '50.00',
        fee: '0.00',
        netAmount: '50.00',
        walletType: 'general' as const,
        toUserId: sampleUsers[1].id,
        status: 'completed' as const,
        reference: 'RECV-001',
        note: 'Received payment for lunch',
        metadata: { transfer_type: 'p2p' },
      },
      // Cashout requests
      {
        id: uuidv4(),
        userId: sampleUsers[1].id,
        type: 'cashout' as const,
        amount: '200.00',
        fee: '4.00',
        netAmount: '196.00',
        walletType: 'general' as const,
        toAgentId: sampleAgent.id,
        status: 'pending' as const,
        reference: 'CASH-001',
        note: 'Need cash for shopping',
        metadata: { agent_commission: '4.00' },
      },
      // Internal transfers
      {
        id: uuidv4(),
        userId: sampleUsers[2].id,
        type: 'internal_transfer' as const,
        amount: '150.00',
        fee: '0.00',
        netAmount: '150.00',
        walletType: 'earning' as const,
        fromWalletType: 'earning' as const,
        toWalletType: 'general' as const,
        status: 'completed' as const,
        reference: 'INT-001',
        note: 'Transfer from earning to general wallet',
        metadata: { transfer_type: 'internal' },
      },
      // Failed transaction
      {
        id: uuidv4(),
        userId: sampleUsers[0].id,
        type: 'deposit' as const,
        amount: '75.00',
        fee: '2.25',
        netAmount: '72.75',
        walletType: 'general' as const,
        status: 'failed' as const,
        paymentGateway: 'stripe',
        reference: 'DEP-004',
        note: 'Card declined',
        metadata: { error_code: 'card_declined', currency: 'USD' },
      },
      // Earning transaction
      {
        id: uuidv4(),
        userId: sampleUsers[2].id,
        type: 'earning' as const,
        amount: '25.00',
        fee: '0.00',
        netAmount: '25.00',
        walletType: 'earning' as const,
        status: 'completed' as const,
        reference: 'EARN-001',
        note: 'Referral bonus',
        metadata: { earning_type: 'referral', referral_id: 'REF123' },
      }
    ];

    // Insert transactions
    for (const transaction of sampleTransactions) {
      try {
        await db.insert(walletTransactions).values(transaction);
        console.log(`✅ Created transaction: ${transaction.reference}`);
      } catch (error) {
        console.log(`ℹ️ Transaction ${transaction.reference} already exists`);
      }
    }

    console.log('✅ Sample transactions created successfully!');
    console.log('📊 Summary:');
    console.log(`   - ${sampleUsers.length} users`);
    console.log(`   - ${sampleGateways.length} payment gateways`);
    console.log(`   - 1 agent`);
    console.log(`   - ${sampleTransactions.length} transactions`);
    console.log('');
    console.log('🌐 You can now visit http://localhost:3001/admin/payments to see the data');

  } catch (error) {
    console.error('❌ Error creating sample transactions:', error);
  }
}

// Run the script
createSampleTransactions().then(() => {
  console.log('🎉 Script completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
