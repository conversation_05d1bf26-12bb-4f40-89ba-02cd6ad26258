import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { executeQuery } from "@/lib/db/mysql";

async function checkAdminUser() {
  try {
    console.log("Checking admin users in the database...");
    
    // Get all users with admin flag
    const adminUsers = await db.query.users.findMany({
      where: eq(users.isAdmin, true),
    });
    
    console.log(`Found ${adminUsers.length} admin users:`);
    adminUsers.forEach(user => {
      console.log(`- ${user.email} (ID: ${user.id}, isAdmin: ${user.isAdmin}, role: ${user.role})`);
    });
    
    // Check if there are any admin users
    if (adminUsers.length === 0) {
      console.log("No admin users found. Checking for users that might need the admin flag...");
      
      // Get all users
      const allUsers = await db.query.users.findMany({
        limit: 10,
      });
      
      console.log(`Found ${allUsers.length} users in the database:`);
      allUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email} (ID: ${user.id}, isAdmin: ${user.isAdmin}, role: ${user.role})`);
      });
      
      if (allUsers.length > 0) {
        // Get the first user to make admin
        const userToMakeAdmin = allUsers[0];
        
        console.log(`\nMaking user ${userToMakeAdmin.email} an admin...`);
        
        // Update the user to be an admin
        await executeQuery(
          "UPDATE users SET is_admin = 1, role = 'admin' WHERE id = ?",
          [userToMakeAdmin.id]
        );
        
        console.log(`User ${userToMakeAdmin.email} is now an admin!`);
        
        // Verify the update
        const updatedUser = await db.query.users.findFirst({
          where: eq(users.id, userToMakeAdmin.id),
        });
        
        console.log(`Updated user: ${updatedUser?.email} (isAdmin: ${updatedUser?.isAdmin}, role: ${updatedUser?.role})`);
      }
    }
    
    console.log("\nDone!");
  } catch (error) {
    console.error("Error checking admin users:", error);
  }
}

// Run the function
checkAdminUser();
