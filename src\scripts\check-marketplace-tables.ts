import { createConnection } from 'mysql2/promise';
import { dbConfig } from '../lib/config';

async function main() {
  console.log("Checking marketplace tables...");
  
  // Create connection
  const connection = await createConnection({
    host: dbConfig.host,
    user: dbConfig.username,
    password: dbConfig.password,
    database: dbConfig.database,
    port: dbConfig.port,
  });
  
  console.log("Connected to database successfully!");
  
  // Check stores table
  const [storesTable] = await connection.execute(`
    SHOW TABLES LIKE 'stores'
  `);
  
  // @ts-ignore
  console.log(`Stores table exists: ${storesTable.length > 0}`);
  
  // Check products table
  const [productsTable] = await connection.execute(`
    SHOW TABLES LIKE 'products'
  `);
  
  // @ts-ignore
  console.log(`Products table exists: ${productsTable.length > 0}`);
  
  // Check store_follows table
  const [storeFollowsTable] = await connection.execute(`
    SHOW TABLES LIKE 'store_follows'
  `);
  
  // @ts-ignore
  console.log(`Store follows table exists: ${storeFollowsTable.length > 0}`);
  
  // Check store_reviews table
  const [storeReviewsTable] = await connection.execute(`
    SHOW TABLES LIKE 'store_reviews'
  `);
  
  // @ts-ignore
  console.log(`Store reviews table exists: ${storeReviewsTable.length > 0}`);
  
  // Check store_settings table
  const [storeSettingsTable] = await connection.execute(`
    SHOW TABLES LIKE 'store_settings'
  `);
  
  // @ts-ignore
  console.log(`Store settings table exists: ${storeSettingsTable.length > 0}`);
  
  // Check foreign keys on store_settings table
  const [foreignKeys] = await connection.execute(`
    SELECT CONSTRAINT_NAME
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE TABLE_NAME = 'store_settings'
    AND CONSTRAINT_TYPE = 'FOREIGN KEY'
    AND CONSTRAINT_SCHEMA = '${dbConfig.database}'
  `);
  
  // @ts-ignore
  console.log(`Store settings foreign key exists: ${foreignKeys.length > 0}`);
  
  await connection.end();
  console.log("Database connection closed.");
}

main().catch((err) => {
  console.error("Error:", err);
  process.exit(1);
});
