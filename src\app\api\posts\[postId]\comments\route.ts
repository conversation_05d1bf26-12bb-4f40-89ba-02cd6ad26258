import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { comments, posts, notifications } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq, desc } from "drizzle-orm";

const commentSchema = z.object({
  content: z.string().min(1).max(1000),
  parentId: z.string().optional(),
});

export async function GET(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;

    // Fetch comments from the database
    const postComments = await db.query.comments.findMany({
      where: eq(comments.postId, postId),
      orderBy: [desc(comments.createdAt)],
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            image: true,
          },
        },
        likes: true,
      },
    });

    // Format comments for the frontend
    const formattedComments = postComments.map((comment) => {
      // Check if the current user has liked/disliked this comment
      const userLike = comment.likes.find(like => like.userId === session.user.id && like.type === 'like');
      const userDislike = comment.likes.find(like => like.userId === session.user.id && like.type === 'dislike');

      // Count likes and dislikes
      const likeCount = comment.likes.filter(like => like.type === 'like').length;
      const dislikeCount = comment.likes.filter(like => like.type === 'dislike').length;

      return {
        id: comment.id,
        content: comment.content,
        createdAt: comment.createdAt.toISOString(),
        parentId: comment.parentId,
        user: comment.user,
        _count: {
          likes: likeCount,
          dislikes: dislikeCount,
          replies: 0, // Will be calculated when organizing comments
        },
        liked: !!userLike,
        disliked: !!userDislike,
        replies: [], // Initialize empty replies array
      };
    });

    // Organize comments into parent-child structure
    const organizeComments = (comments: any[]) => {
      const commentMap = new Map();
      const rootComments: any[] = [];

      // First pass: create map of all comments
      comments.forEach(comment => {
        commentMap.set(comment.id, { ...comment, replies: [] });
      });

      // Second pass: organize into parent-child structure
      comments.forEach(comment => {
        const commentWithReplies = commentMap.get(comment.id);

        if (comment.parentId) {
          const parent = commentMap.get(comment.parentId);
          if (parent) {
            parent.replies = parent.replies || [];
            parent.replies.push(commentWithReplies);
            // Update parent's reply count
            parent._count.replies = parent.replies.length;
          }
        } else {
          rootComments.push(commentWithReplies);
        }
      });

      // Sort replies by creation date (oldest first for better conversation flow)
      const sortReplies = (comments: any[]) => {
        comments.forEach(comment => {
          if (comment.replies && comment.replies.length > 0) {
            comment.replies.sort((a: any, b: any) =>
              new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
            );
            sortReplies(comment.replies);
          }
        });
      };

      sortReplies(rootComments);
      return rootComments;
    };

    const organizedComments = organizeComments(formattedComments);
    return NextResponse.json(organizedComments);
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;
    const body = await req.json();
    const validatedData = commentSchema.parse(body);

    const commentId = uuidv4();

    // Get the post to check the owner
    const post = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
      columns: {
        id: true,
        userId: true,
      },
    });

    if (!post) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Insert the comment into the database
    await db.insert(comments).values({
      id: commentId,
      content: validatedData.content,
      userId: session.user.id,
      postId,
      parentId: validatedData.parentId || null,
    });

    // Create notification for post owner (if not commenting on own post)
    if (post.userId !== session.user.id) {
      await db.insert(notifications).values({
        id: uuidv4(),
        recipientId: post.userId,
        type: "comment",
        senderId: session.user.id,
        postId,
        commentId,
        read: false,
      });
    }

    return NextResponse.json(
      { message: "Comment created successfully", id: commentId },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating comment:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: "Invalid input data", errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
