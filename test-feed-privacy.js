// Test script to check feed privacy filtering
// Run this in browser console after logging in

async function testFeedPrivacy() {
  console.log('Testing feed privacy filtering...');
  
  try {
    // Test first page
    const response1 = await fetch('/api/feed?page=1&limit=10');
    const data1 = await response1.json();
    
    console.log('Page 1 Results:');
    console.log('- Posts returned:', data1.data?.length || 0);
    console.log('- Has more:', data1.pagination?.hasMore);
    console.log('- Total count:', data1.pagination?.total);
    console.log('- Total pages:', data1.pagination?.totalPages);
    
    // Check privacy levels
    const privacyBreakdown = {};
    data1.data?.forEach(post => {
      privacyBreakdown[post.privacy] = (privacyBreakdown[post.privacy] || 0) + 1;
    });
    console.log('- Privacy breakdown:', privacyBreakdown);
    
    // Test second page if available
    if (data1.pagination?.hasMore) {
      const response2 = await fetch('/api/feed?page=2&limit=10');
      const data2 = await response2.json();
      
      console.log('\nPage 2 Results:');
      console.log('- Posts returned:', data2.data?.length || 0);
      console.log('- Has more:', data2.pagination?.hasMore);
      
      const privacyBreakdown2 = {};
      data2.data?.forEach(post => {
        privacyBreakdown2[post.privacy] = (privacyBreakdown2[post.privacy] || 0) + 1;
      });
      console.log('- Privacy breakdown:', privacyBreakdown2);
    }
    
    // Test posts API for comparison
    const postsResponse = await fetch('/api/posts?page=1&limit=10');
    const postsData = await postsResponse.json();
    
    console.log('\nPosts API (for comparison):');
    console.log('- Posts returned:', postsData.data?.length || 0);
    console.log('- Has more:', postsData.pagination?.hasMore);
    console.log('- Total count:', postsData.pagination?.total);
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testFeedPrivacy();
