# Comment Features Enhancement

এই ডকুমেন্টেশনে HIFNF প্ল্যাটফর্মের নতুন comment features গুলোর বিস্তারিত বর্ণনা রয়েছে।

## 🆕 নতুন Features

### 1. Comment Reply System
- **Nested Comments**: এখন comments এর reply করা যাবে
- **Parent-Child Structure**: Comments গুলো parent-child relationship এ organize হবে
- **Reply UI**: প্রতিটি comment এর নিচে Reply button এবং reply form
- **Visual Hierarchy**: Reply গুলো indented হয়ে দেখানো হবে

### 2. Comment Like/Dislike System
- **Like Comments**: Comments এ like দেওয়া যাবে
- **Dislike Comments**: Comments এ dislike দেওয়া যাবে
- **Real-time Count**: Like/dislike count real-time update হবে
- **User Status**: User এর like/dislike status track করা হবে

### 3. Enhanced Comment Display
- **Expandable Replies**: Reply গুলো expand/collapse করা যাবে
- **Reply Count**: কতগুলো reply আছে তা দেখানো হবে
- **Better UI**: Improved visual design এবং user experience

## 🔧 Technical Implementation

### Database Schema Updates

#### Comments Table
```sql
-- Existing comments table supports parentId for replies
comments {
  id: varchar(255) PRIMARY KEY
  content: text NOT NULL
  userId: varchar(255) NOT NULL
  postId: varchar(255) NOT NULL
  parentId: varchar(255) NULL  -- For reply functionality
  createdAt: timestamp
  updatedAt: timestamp
}
```

#### Likes Table
```sql
-- Enhanced likes table supports comment likes/dislikes
likes {
  id: varchar(255) PRIMARY KEY
  userId: varchar(255) NOT NULL
  postId: varchar(255) NULL
  commentId: varchar(255) NULL  -- For comment likes/dislikes
  type: enum('like', 'dislike') DEFAULT 'like'
  createdAt: timestamp
}
```

### API Endpoints

#### Comment Like/Dislike
- `POST /api/comments/[commentId]/like` - Like/unlike a comment
- `POST /api/comments/[commentId]/dislike` - Dislike/undislike a comment
- `POST /api/blogs/[slug]/comments/[commentId]/like` - Like blog comment
- `POST /api/blogs/[slug]/comments/[commentId]/dislike` - Dislike blog comment
- `POST /api/fan-pages/posts/comments/[commentId]/dislike` - Dislike fan page comment (limited support)

#### Comment Replies
- `POST /api/posts/[postId]/comments` - Create comment/reply (with parentId)
- `POST /api/blogs/[slug]/comments` - Create blog comment/reply
- `POST /api/fan-pages/posts/[postId]/comments` - Create fan page comment/reply

### Component Updates

#### CommentSection Component
- **Enhanced Interface**: Updated Comment interface with reply support
- **Reply Functionality**: handleSubmitReply function for posting replies
- **Like/Dislike**: handleLikeComment and handleDislikeComment functions
- **Nested Rendering**: Recursive comment rendering with replies
- **State Management**: Optimistic updates for better UX

#### FanPageCommentSection Component
- **Dislike Support**: Added dislike button (limited backend support)
- **Enhanced UI**: Better visual hierarchy for comments and replies
- **Reply System**: Full reply functionality for fan page comments

## 🎯 Usage Examples

### Basic Comment with Reply
```typescript
// Parent comment
{
  id: "comment-1",
  content: "This is a parent comment",
  parentId: null,
  replies: [
    {
      id: "reply-1",
      content: "This is a reply",
      parentId: "comment-1",
      replies: []
    }
  ]
}
```

### Comment with Like/Dislike
```typescript
{
  id: "comment-1",
  content: "Great post!",
  _count: {
    likes: 5,
    dislikes: 1,
    replies: 2
  },
  liked: true,
  disliked: false
}
```

## 🧪 Testing

### Manual Testing
1. একটি post এ comment করুন
2. Comment এ reply করুন
3. Comment এ like/dislike দিন
4. Reply গুলো expand/collapse করুন

## 🚀 Deployment Notes

### Database Migration
নতুন features ব্যবহার করার জন্য কোনো additional database migration প্রয়োজন নেই কারণ:
- `comments.parentId` field ইতিমধ্যে exists
- `likes.commentId` এবং `likes.type` fields ইতিমধ্যে exists

### Environment Variables
কোনো নতুন environment variables প্রয়োজন নেই।

### Performance Considerations
- Comment queries এ proper indexing ensure করুন
- Large comment threads এর জন্য pagination implement করুন
- Real-time updates এর জন্য caching strategy consider করুন

## 🐛 Known Limitations

1. **Fan Page Comments Dislike**: Fan page comments এর জন্য dislike functionality partially implemented
2. **Deep Nesting**: Very deep comment nesting এর জন্য UI optimization প্রয়োজন
3. **Real-time Updates**: WebSocket integration নেই, manual refresh প্রয়োজন

## 🔮 Future Enhancements

1. **Real-time Updates**: WebSocket integration for live comment updates
2. **Comment Moderation**: Admin tools for comment management
3. **Rich Text Comments**: Support for formatting, mentions, hashtags
4. **Comment Reactions**: Emoji reactions beyond like/dislike
5. **Comment Threading**: Better UI for deep comment threads
6. **Comment Search**: Search functionality within comments

## 📝 Code Examples

### Using Enhanced CommentSection
```tsx
<CommentSection 
  postId={post.id}
  postType="user_post"
  // Automatically handles replies and like/dislike
/>
```

### Using FanPageCommentSection
```tsx
<FanPageCommentSection
  postId={fanPagePost.id}
  showComments={true}
  onCommentCountChange={(count) => setCommentCount(count)}
/>
```

## 🤝 Contributing

নতুন comment features এ contribute করতে:
1. Feature branch তৈরি করুন
2. Tests লিখুন এবং run করুন
3. Documentation update করুন
4. Pull request submit করুন

---

**Note**: এই features গুলো HIFNF platform এর user engagement এবং interaction significantly improve করবে।
