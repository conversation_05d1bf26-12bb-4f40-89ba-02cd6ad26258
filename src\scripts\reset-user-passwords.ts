import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function resetUserPasswords() {
  console.log('Resetting user passwords for first-time login setup...');

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_db";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbHost,
      user: dbUser,
      password: dbPassword,
      database: dbName,
      port: dbPort,
    });

    console.log(`Connected to database '${dbName}'`);
    
    // Check current password status
    const [beforeUsers] = await connection.execute(
      "SELECT COUNT(*) as total, SUM(CASE WHEN password IS NOT NULL AND password != '' THEN 1 ELSE 0 END) as with_password FROM users"
    ) as any;
    
    console.log("Before reset:");
    console.log(`Total users: ${beforeUsers[0].total}`);
    console.log(`Users with password: ${beforeUsers[0].with_password}`);
    
    // Reset all user passwords to NULL for first-time login
    const [result] = await connection.execute(
      "UPDATE users SET password = NULL"
    ) as any;
    
    console.log(`\nPassword reset completed. ${result.affectedRows} users updated.`);
    
    // Check after reset
    const [afterUsers] = await connection.execute(
      "SELECT COUNT(*) as total, SUM(CASE WHEN password IS NOT NULL AND password != '' THEN 1 ELSE 0 END) as with_password FROM users"
    ) as any;
    
    console.log("\nAfter reset:");
    console.log(`Total users: ${afterUsers[0].total}`);
    console.log(`Users with password: ${afterUsers[0].with_password}`);

    // Close the connection
    await connection.end();
    console.log("\nReset completed successfully!");
    console.log("All existing users can now login with any password on their first attempt.");
    console.log("The password they use will become their permanent password.");

  } catch (error) {
    console.error("Reset failed:", error);
    process.exit(1);
  }
}

// Run the reset
resetUserPasswords();
