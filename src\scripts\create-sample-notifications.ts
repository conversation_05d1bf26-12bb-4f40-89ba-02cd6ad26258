import { db } from "@/lib/db";
import { notifications, users } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";

async function createSampleNotifications() {
  try {
    console.log("Creating sample notifications...");

    // Get some users from the database
    const allUsers = await db.query.users.findMany({
      columns: {
        id: true,
        name: true,
        image: true,
      },
      limit: 5,
    });

    if (allUsers.length < 2) {
      console.log("Need at least 2 users in the database to create sample notifications");
      return;
    }

    const [user1, user2, ...otherUsers] = allUsers;

    // Create sample notifications for user1
    const sampleNotifications = [
      {
        id: uuidv4(),
        recipientId: user1.id,
        type: "like" as const,
        senderId: user2.id,
        read: false,
      },
      {
        id: uuidv4(),
        recipientId: user1.id,
        type: "comment" as const,
        senderId: user2.id,
        read: false,
      },
      {
        id: uuidv4(),
        recipientId: user1.id,
        type: "subscription" as const,
        senderId: user2.id,
        read: true,
      },
      {
        id: uuidv4(),
        recipientId: user1.id,
        type: "message" as const,
        senderId: user2.id,
        read: false,
      },
    ];

    // Add more notifications if we have more users
    if (otherUsers.length > 0) {
      sampleNotifications.push({
        id: uuidv4(),
        recipientId: user1.id,
        type: "like" as const,
        senderId: otherUsers[0].id,
        read: false,
      });
    }

    // Insert notifications
    await db.insert(notifications).values(sampleNotifications);

    console.log(`✅ Created ${sampleNotifications.length} sample notifications for user: ${user1.name}`);
    console.log("Sample notifications created successfully!");

  } catch (error) {
    console.error("Error creating sample notifications:", error);
  }
}

// Run the script
createSampleNotifications();
