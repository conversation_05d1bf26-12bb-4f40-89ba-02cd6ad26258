"use client";

import { PrinterIcon } from "@heroicons/react/24/outline";

interface PrintButtonProps {
  title: string;
  author: string;
  content: string;
}

export function PrintButton({ title, author, content }: PrintButtonProps) {
  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    // Generate print-friendly HTML
    const printHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            body {
              font-family: 'Times New Roman', serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            h1 {
              color: #2563eb;
              border-bottom: 2px solid #e5e7eb;
              padding-bottom: 10px;
              margin-bottom: 20px;
            }
            h2 {
              color: #374151;
              margin-top: 30px;
              margin-bottom: 15px;
            }
            h3 {
              color: #4b5563;
              margin-top: 25px;
              margin-bottom: 10px;
            }
            p {
              margin-bottom: 15px;
              text-align: justify;
            }
            .header {
              text-align: center;
              margin-bottom: 40px;
              border-bottom: 1px solid #e5e7eb;
              padding-bottom: 20px;
            }
            .author {
              color: #6b7280;
              font-style: italic;
              margin-top: 10px;
            }
            .print-date {
              color: #9ca3af;
              font-size: 12px;
              margin-top: 10px;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <div class="author">By ${author}</div>
            <div class="print-date">Printed on ${new Date().toLocaleDateString()}</div>
          </div>
          <div class="content">
            ${content.split('\n').map(paragraph => {
              if (paragraph.startsWith('# ')) {
                return `<h1>${paragraph.substring(2)}</h1>`;
              }
              if (paragraph.startsWith('## ')) {
                return `<h2>${paragraph.substring(3)}</h2>`;
              }
              if (paragraph.startsWith('### ')) {
                return `<h3>${paragraph.substring(4)}</h3>`;
              }
              if (paragraph.startsWith('- ')) {
                return `<li>${paragraph.substring(2)}</li>`;
              }
              if (paragraph.trim() === '') {
                return '<br>';
              }
              return `<p>${paragraph}</p>`;
            }).join('')}
          </div>
        </body>
      </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();
    
    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  };

  return (
    <button
      onClick={handlePrint}
      className="p-3 text-gray-600 hover:text-blue-600 transition-all duration-300 hover:bg-blue-50 rounded-full group"
      title="Print this post"
    >
      <PrinterIcon className="w-6 h-6 group-hover:scale-110 transition-transform" />
    </button>
  );
}
