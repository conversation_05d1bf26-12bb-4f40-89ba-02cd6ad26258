import { db } from '../lib/db';
import { walletTransactions } from '../lib/db/schema';
import { v4 as uuidv4 } from 'uuid';

async function updateSampleTransactions() {
  try {
    console.log('🚀 Updating sample transactions...');

    // Delete existing transactions to avoid conflicts
    await db.delete(walletTransactions);
    console.log('🗑️ Cleared existing transactions');

    // Get existing users
    const users = await db.query.users.findMany({
      limit: 3
    });

    if (users.length < 3) {
      console.log('❌ Not enough users found. Please run create-sample-transactions.ts first');
      return;
    }

    // Get existing agent
    const agents = await db.query.agents.findMany({
      limit: 1
    });

    const agentId = agents.length > 0 ? agents[0].id : null;

    // Create updated sample transactions
    const sampleTransactions: any[] = [
      // Deposits
      {
        id: uuidv4(),
        userId: users[0].id,
        type: 'deposit' as const,
        amount: '100.00',
        fee: '2.80',
        netAmount: '97.20',
        walletType: 'general' as const,
        status: 'completed' as const,
        paymentGateway: 'stripe',
        gatewayTransactionId: 'pi_1234567890',
        reference: 'DEP-001',
        note: 'First deposit via Stripe',
        metadata: { gateway_fee: '2.80', currency: 'USD' },
      },
      {
        id: uuidv4(),
        userId: users[1].id,
        type: 'deposit' as const,
        amount: '250.00',
        fee: '7.50',
        netAmount: '242.50',
        walletType: 'general' as const,
        status: 'pending' as const,
        paymentGateway: 'paypal',
        gatewayTransactionId: 'PAYID-123456',
        reference: 'DEP-002',
        note: 'PayPal deposit pending verification',
        metadata: { gateway_fee: '7.50', currency: 'USD' },
      },
      {
        id: uuidv4(),
        userId: users[2].id,
        type: 'deposit' as const,
        amount: '500.00',
        fee: '0.00',
        netAmount: '500.00',
        walletType: 'general' as const,
        status: 'processing' as const,
        paymentGateway: 'manual',
        reference: 'DEP-003',
        note: 'Manual bank transfer',
        metadata: { bank_reference: 'TXN789123', currency: 'USD' },
      },
      // Send money transactions
      {
        id: uuidv4(),
        userId: users[0].id,
        type: 'send' as const,
        amount: '50.00',
        fee: '1.00',
        netAmount: '49.00',
        walletType: 'general' as const,
        toUserId: users[1].id,
        status: 'completed' as const,
        reference: 'SEND-001',
        note: 'Payment for lunch',
        metadata: { transfer_type: 'p2p' },
      },
      {
        id: uuidv4(),
        userId: users[1].id,
        type: 'receive' as const,
        amount: '50.00',
        fee: '0.00',
        netAmount: '50.00',
        walletType: 'general' as const,
        status: 'completed' as const,
        reference: 'RECV-001',
        note: 'Received payment for lunch',
        metadata: { transfer_type: 'p2p' },
      },
      // Internal transfers
      {
        id: uuidv4(),
        userId: users[2].id,
        type: 'internal_transfer' as const,
        amount: '150.00',
        fee: '0.00',
        netAmount: '150.00',
        walletType: 'earning' as const,
        fromWalletType: 'earning' as const,
        toWalletType: 'general' as const,
        status: 'completed' as const,
        reference: 'INT-001',
        note: 'Transfer from earning to general wallet',
        metadata: { transfer_type: 'internal' },
      },
      // Failed transaction
      {
        id: uuidv4(),
        userId: users[0].id,
        type: 'deposit' as const,
        amount: '75.00',
        fee: '2.25',
        netAmount: '72.75',
        walletType: 'general' as const,
        status: 'failed' as const,
        paymentGateway: 'stripe',
        reference: 'DEP-004',
        note: 'Card declined',
        metadata: { error_code: 'card_declined', currency: 'USD' },
      },
      // Earning transaction
      {
        id: uuidv4(),
        userId: users[2].id,
        type: 'earning' as const,
        amount: '25.00',
        fee: '0.00',
        netAmount: '25.00',
        walletType: 'earning' as const,
        status: 'completed' as const,
        reference: 'EARN-001',
        note: 'Referral bonus',
        metadata: { earning_type: 'referral', referral_id: 'REF123' },
      }
    ];

    // Add withdraw transaction if agent exists
    if (agentId) {
      sampleTransactions.push({
        id: uuidv4(),
        userId: users[1].id,
        type: 'withdraw' as const,
        amount: '200.00',
        fee: '4.00',
        netAmount: '196.00',
        walletType: 'general' as const,
        toAgentId: agentId,
        status: 'pending' as const,
        reference: 'CASH-001',
        note: 'Need cash for shopping',
        metadata: { gateway_fee: '4.00', currency: 'USD' },
      });
    }

    // Insert transactions
    for (const transaction of sampleTransactions) {
      await db.insert(walletTransactions).values(transaction);
      console.log(`✅ Created transaction: ${transaction.reference}`);
    }

    console.log('✅ Sample transactions updated successfully!');
    console.log(`📊 Created ${sampleTransactions.length} transactions`);

  } catch (error) {
    console.error('❌ Error updating sample transactions:', error);
  }
}

// Run the script
updateSampleTransactions().then(() => {
  console.log('🎉 Script completed!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
