"use client";

import { Fragment, useState } from "react";
import { signOut } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { Menu, Transition } from "@headlessui/react";
import {
  Bars3Icon,
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  Cog6ToothIcon,
  HomeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  QuestionMarkCircleIcon,
  UsersIcon,
  FlagIcon,
  NewspaperIcon,
  StarIcon,
} from "@heroicons/react/24/outline";
import { SessionStatusIndicator } from "./SessionStatusIndicator";

interface AdminHeaderProps {
  onMenuClick: () => void;
  user: any;
}

export function AdminHeader({ onMenuClick, user }: AdminHeaderProps) {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <header className="sticky top-0 z-10 bg-white shadow-sm lg:pl-72">
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        {/* Left side - Menu button and logo */}
        <div className="flex items-center">
          <button
            type="button"
            className="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden"
            onClick={onMenuClick}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>

          <div className="ml-4 flex lg:ml-0">
            <Link href="/admin/dashboard">
              <div className="flex items-center">
                <div className="flex h-8 w-8 items-center justify-center rounded-md bg-blue-600 text-white">
                  <span className="text-lg font-bold">H</span>
                </div>
                <span className="ml-2 text-lg font-semibold text-gray-900">HIFNF <span className="text-blue-600">Admin</span></span>
              </div>
            </Link>
          </div>

          {/* Search bar */}
          <div className="ml-6 hidden md:block lg:max-w-xs xl:max-w-lg 2xl:max-w-2xl">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                className="block w-full rounded-full border border-gray-300 bg-gray-50 py-2 pl-10 pr-3 text-sm placeholder-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Search in admin panel..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Quick action buttons */}
        <div className="hidden md:flex items-center space-x-2">
          <Link href="/admin/users/new" className="flex items-center rounded-md bg-gray-50 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600" title="Add User">
            <UsersIcon className="mr-1.5 h-4 w-4" />
            <span>Add User</span>
          </Link>
          <Link href="/admin/posts/new" className="flex items-center rounded-md bg-gray-50 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600" title="Add Post">
            <NewspaperIcon className="mr-1.5 h-4 w-4" />
            <span>New Post</span>
          </Link>
          <Link href="/admin/fan-pages" className="flex items-center rounded-md bg-gray-50 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600" title="Fan Pages">
            <StarIcon className="mr-1.5 h-4 w-4" />
            <span>Fan Pages</span>
          </Link>
          <Link href="/admin/posts/settings" className="flex items-center rounded-md bg-gray-50 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600" title="Feed Settings">
            <Cog6ToothIcon className="mr-1.5 h-4 w-4" />
            <span>Feed Settings</span>
          </Link>
          <Link href="/admin/reports" className="flex items-center rounded-md bg-gray-50 px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-600" title="View Reports">
            <FlagIcon className="mr-1.5 h-4 w-4" />
            <span>Reports</span>
          </Link>
          <button className="flex items-center rounded-md bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-700 hover:bg-blue-100" title="Help">
            <QuestionMarkCircleIcon className="mr-1.5 h-4 w-4" />
            <span>Help</span>
          </button>
        </div>

        {/* Right side - Session status, Notifications and profile */}
        <div className="flex items-center space-x-4">
          {/* Session Status Indicator */}
          <div className="hidden lg:block">
            <SessionStatusIndicator />
          </div>
          {/* Notifications */}
          <button
            type="button"
            className="relative rounded-full p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <span className="sr-only">View notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
            <span className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white">3</span>
          </button>

          {/* Profile dropdown */}
          <Menu as="div" className="relative">
            <Menu.Button className="flex items-center rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
              <span className="sr-only">Open user menu</span>
              {user.image ? (
                <Image
                  src={user.image}
                  alt={user.name || "User"}
                  width={32}
                  height={32}
                  className="h-8 w-8 rounded-full"
                />
              ) : (
                <UserCircleIcon className="h-8 w-8 text-gray-500" />
              )}
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div className="px-4 py-2">
                  <p className="text-sm font-medium text-gray-900">{user.name}</p>
                  <p className="truncate text-xs text-gray-500">{user.email}</p>
                  {user.adminRoleName && (
                    <p className="mt-1 text-xs font-medium text-blue-600">
                      {user.adminRoleName}
                    </p>
                  )}
                </div>
                <hr className="my-1" />
                <Menu.Item>
                  {({ active }) => (
                    <Link
                      href="/"
                      className={`${
                        active ? "bg-gray-100" : ""
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <HomeIcon className="mr-3 h-5 w-5 text-gray-500" />
                      Main Site
                    </Link>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <Link
                      href="/admin/profile"
                      className={`${
                        active ? "bg-gray-100" : ""
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <UserCircleIcon className="mr-3 h-5 w-5 text-gray-500" />
                      Your Profile
                    </Link>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <Link
                      href="/admin/settings"
                      className={`${
                        active ? "bg-gray-100" : ""
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <Cog6ToothIcon className="mr-3 h-5 w-5 text-gray-500" />
                      Settings
                    </Link>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={() => signOut({ callbackUrl: "/admin/login" })}
                      className={`${
                        active ? "bg-gray-100" : ""
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5 text-gray-500" />
                      Sign out
                    </button>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </div>
    </header>
  );
}
