import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { posts } from "@/lib/db/schema";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";
import { eq } from "drizzle-orm";

const sharePostSchema = z.object({
  shareType: z.enum(["timeline", "withContent"]),
  content: z.string().max(5000).optional(),
  privacy: z.enum(["public", "friends", "private"]).default("public"),
});

export async function POST(
  req: Request,
  context: { params: Promise<{ postId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the post ID from the context params
    const params = await context.params;
    const { postId } = params;
    const body = await req.json();
    const validatedData = sharePostSchema.parse(body);

    // Check if the original post exists
    const originalPost = await db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        user: {
          columns: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!originalPost) {
      return NextResponse.json(
        { message: "Post not found" },
        { status: 404 }
      );
    }

    // Create a new post ID
    const newPostId = uuidv4();

    // Create a new post that references the original post
    let shareContent = "";

    if (validatedData.shareType === "withContent") {
      // If sharing with content, use the provided content
      shareContent = validatedData.content || "";
    } else {
      // If sharing directly to timeline, create a simple share message
      shareContent = `Shared a post from ${originalPost.user.name}`;
    }

    // Insert the shared post into the database
    await db.insert(posts).values({
      id: newPostId,
      userId: session.user.id,
      content: shareContent,
      images: originalPost.images, // Copy the original post's images
      privacy: validatedData.privacy,
      sharedPostId: postId, // Reference to the original post
    });

    return NextResponse.json(
      {
        message: "Post shared successfully",
        id: newPostId
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error sharing post:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
