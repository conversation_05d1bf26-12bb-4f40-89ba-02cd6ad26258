"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  parentId?: string | null;
  user: {
    id: string;
    name: string;
    image: string | null;
  };
  _count?: {
    likes: number;
    dislikes: number;
    replies: number;
  };
  liked?: boolean;
  disliked?: boolean;
  replies?: Comment[];
}

interface CommentDebugProps {
  postId: string;
}

export function CommentDebug({ postId }: CommentDebugProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");

  const fetchComments = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/posts/${postId}/comments`);
      if (response.ok) {
        const data = await response.json();
        console.log("Raw API Response:", data);
        setComments(data);
      }
    } catch (error) {
      console.error("Error fetching comments:", error);
    } finally {
      setLoading(false);
    }
  };

  const submitComment = async () => {
    if (!newComment.trim()) return;
    
    try {
      const response = await fetch(`/api/posts/${postId}/comments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: newComment }),
      });
      
      if (response.ok) {
        setNewComment("");
        fetchComments();
      }
    } catch (error) {
      console.error("Error posting comment:", error);
    }
  };

  const submitReply = async (parentId: string) => {
    if (!replyContent.trim()) return;
    
    try {
      const response = await fetch(`/api/posts/${postId}/comments`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          content: replyContent,
          parentId: parentId
        }),
      });
      
      if (response.ok) {
        setReplyContent("");
        setReplyingTo(null);
        fetchComments();
      }
    } catch (error) {
      console.error("Error posting reply:", error);
    }
  };

  useEffect(() => {
    if (postId) {
      fetchComments();
    }
  }, [postId]);

  const renderComment = (comment: Comment, level = 0) => (
    <div key={comment.id} className={`border-l-2 border-gray-200 pl-4 mb-4 ${level > 0 ? 'ml-6' : ''}`}>
      <div className="bg-gray-50 p-3 rounded">
        <div className="font-semibold text-sm">{comment.user.name}</div>
        <div className="text-gray-700 mt-1">{comment.content}</div>
        <div className="text-xs text-gray-500 mt-2">
          {comment.parentId ? `Reply to: ${comment.parentId}` : 'Parent Comment'}
        </div>
        <div className="text-xs text-gray-400 mt-1">
          Likes: {comment._count?.likes || 0} | 
          Dislikes: {comment._count?.dislikes || 0} | 
          Replies: {comment._count?.replies || 0}
        </div>
        <button
          onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
          className="text-blue-600 text-xs mt-2 hover:underline"
        >
          {replyingTo === comment.id ? 'Cancel Reply' : 'Reply'}
        </button>
        
        {replyingTo === comment.id && (
          <div className="mt-2 flex gap-2">
            <input
              type="text"
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              placeholder="Write a reply..."
              className="flex-1 px-2 py-1 border rounded text-sm"
            />
            <button
              onClick={() => submitReply(comment.id)}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
            >
              Reply
            </button>
          </div>
        )}
      </div>
      
      {/* Render replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-2">
          <div className="text-xs text-gray-600 mb-2">
            {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}:
          </div>
          {comment.replies.map(reply => renderComment(reply, level + 1))}
        </div>
      )}
    </div>
  );

  if (!session) {
    return <div className="p-4 text-gray-500">Please login to view comments</div>;
  }

  return (
    <div className="max-w-2xl mx-auto p-4">
      <h2 className="text-xl font-bold mb-4">Comment Debug Tool</h2>
      <div className="text-sm text-gray-600 mb-4">Post ID: {postId}</div>
      
      {/* Add Comment Form */}
      <div className="mb-6 p-4 border rounded">
        <h3 className="font-semibold mb-2">Add New Comment</h3>
        <div className="flex gap-2">
          <input
            type="text"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Write a comment..."
            className="flex-1 px-3 py-2 border rounded"
          />
          <button
            onClick={submitComment}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Post
          </button>
        </div>
      </div>

      {/* Refresh Button */}
      <button
        onClick={fetchComments}
        disabled={loading}
        className="mb-4 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
      >
        {loading ? 'Loading...' : 'Refresh Comments'}
      </button>

      {/* Comments Display */}
      <div className="space-y-4">
        <h3 className="font-semibold">Comments ({comments.length}):</h3>
        {comments.length === 0 ? (
          <div className="text-gray-500 italic">No comments yet</div>
        ) : (
          comments.map(comment => renderComment(comment))
        )}
      </div>

      {/* Raw Data Display */}
      <details className="mt-8 p-4 border rounded">
        <summary className="font-semibold cursor-pointer">Raw API Data</summary>
        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
          {JSON.stringify(comments, null, 2)}
        </pre>
      </details>
    </div>
  );
}
