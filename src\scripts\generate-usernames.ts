import mysql from "mysql2/promise";
import * as dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";

dotenv.config();

async function main() {
  console.log("Generating usernames for existing users...");

  // Get database credentials from .env file
  const dbHost = process.env.DATABASE_HOST || "localhost";
  const dbUser = process.env.DATABASE_USERNAME || "root";
  const dbPassword = process.env.DATABASE_PASSWORD || "";
  const dbName = process.env.DATABASE_NAME || "hifnf_drizzle";
  const dbPort = parseInt(process.env.DATABASE_PORT || "3306");

  console.log(`Connecting to MySQL at ${dbHost}:${dbPort} as ${dbUser}...`);

  try {
    // Connect to the database
    const connection = await mysql.createConnection({
      host: dbH<PERSON>,
      user: dbU<PERSON>,
      password: dbPassword,
      database: dbName,
      port: dbPort,
      multipleStatements: true,
    });

    console.log(`Connected to database '${dbName}'`);

    // Check if username column exists
    const [columns] = await connection.execute(
      "SHOW COLUMNS FROM users LIKE 'username'"
    );

    // @ts-ignore
    if (columns.length === 0) {
      console.log("Username column doesn't exist. Please run the add-username script first.");
      process.exit(1);
    }

    // Get all users without a username
    const [users] = await connection.execute(
      "SELECT id, name, email FROM users WHERE username IS NULL OR username = ''"
    );

    // Ensure users is an array
    const usersArray = Array.isArray(users) ? users : [];

    if (usersArray.length === 0) {
      console.log("All users already have usernames.");
      await connection.end();
      return;
    }

    console.log(`Found ${usersArray.length} users without usernames.`);

    // Generate and update usernames for each user
    for (const user of usersArray as { id: string, name: string | null, email: string }[]) {
      try {
        // Generate a username based on name or email
        let baseUsername = '';

        if (user.name) {
          // Use name: convert to lowercase, replace spaces with underscores, remove special chars
          baseUsername = user.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
        } else {
          // Use email: take part before @ and remove special chars
          baseUsername = user.email.split('@')[0].replace(/[^a-z0-9_]/g, '');
        }

        // If baseUsername is empty or too short, use a default
        if (!baseUsername || baseUsername.length < 3) {
          baseUsername = 'user';
        }

        // Try the base username first
        let username = baseUsername;
        let usernameAvailable = false;
        let attempts = 0;

        while (!usernameAvailable && attempts < 10) {
          // Check if username is available
          const [existingUsers] = await connection.execute(
            "SELECT id FROM users WHERE username = ?",
            [username]
          );

          // @ts-ignore
          if (existingUsers.length === 0) {
            usernameAvailable = true;
          } else {
            // Add a random suffix
            username = `${baseUsername}_${Math.floor(Math.random() * 1000)}`;
            attempts++;
          }
        }

        // If we couldn't find an available username after 10 attempts, use a UUID-based one
        if (!usernameAvailable) {
          username = `user_${uuidv4().substring(0, 8)}`;
        }

        // Update the user's username
        await connection.execute(
          "UPDATE users SET username = ? WHERE id = ?",
          [username, user.id]
        );

        console.log(`Updated user ${user.id}: username = ${username}`);
      } catch (error) {
        console.error(`Error updating username for user ${user.id}:`, error);
      }
    }

    console.log("Username generation completed.");
    await connection.end();

  } catch (error) {
    console.error("Error generating usernames:", error);
    process.exit(1);
  }
}

main();
