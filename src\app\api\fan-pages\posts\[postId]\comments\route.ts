import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { fanPagePostComments, fanPagePosts, users, fanPages } from "@/lib/db/schema";
import { eq, desc, and, count } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

interface RouteParams {
  params: Promise<{
    postId: string;
  }>;
}

const commentSchema = z.object({
  content: z.string().min(1, "Comment cannot be empty").max(1000, "Comment too long"),
  parentId: z.string().optional(),
  asPage: z.boolean().optional(), // Whether to comment as a fan page
  fanPageId: z.string().optional(), // Fan page ID if commenting as page
});

// GET /api/fan-pages/posts/[postId]/comments - Get comments for a fan page post
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { postId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = Math.min(50, parseInt(searchParams.get("limit") || "20"));
    const offset = (page - 1) * limit;

    // Check if post exists
    const post = await db
      .select()
      .from(fanPagePosts)
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (post.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Get comments with user and fan page info
    const comments = await db
      .select({
        id: fanPagePostComments.id,
        content: fanPagePostComments.content,
        parentId: fanPagePostComments.parentId,
        likeCount: fanPagePostComments.likeCount,
        createdAt: fanPagePostComments.createdAt,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
        fanPage: {
          id: fanPages.id,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePostComments)
      .leftJoin(users, eq(fanPagePostComments.userId, users.id))
      .leftJoin(fanPages, eq(fanPagePostComments.fanPageId, fanPages.id))
      .where(eq(fanPagePostComments.postId, postId))
      .orderBy(desc(fanPagePostComments.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(fanPagePostComments)
      .where(eq(fanPagePostComments.postId, postId));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      comments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });

  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch comments" },
      { status: 500 }
    );
  }
}

// POST /api/fan-pages/posts/[postId]/comments - Create a new comment
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { postId } = await params;
    const body = await request.json();
    const validatedData = commentSchema.parse(body);

    // Check if post exists
    const post = await db
      .select()
      .from(fanPagePosts)
      .where(eq(fanPagePosts.id, postId))
      .limit(1);

    if (post.length === 0) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // If commenting as a fan page, verify ownership
    let fanPageId = null;
    if (validatedData.asPage && validatedData.fanPageId) {
      const fanPage = await db
        .select()
        .from(fanPages)
        .where(
          and(
            eq(fanPages.id, validatedData.fanPageId),
            eq(fanPages.ownerId, session.user.id),
            eq(fanPages.isActive, true)
          )
        )
        .limit(1);

      if (fanPage.length === 0) {
        return NextResponse.json(
          { error: "Fan page not found or you don't have permission" },
          { status: 403 }
        );
      }

      fanPageId = validatedData.fanPageId;
    }

    // If parentId is provided, verify parent comment exists
    if (validatedData.parentId) {
      const parentComment = await db
        .select()
        .from(fanPagePostComments)
        .where(eq(fanPagePostComments.id, validatedData.parentId))
        .limit(1);

      if (parentComment.length === 0) {
        return NextResponse.json(
          { error: "Parent comment not found" },
          { status: 404 }
        );
      }
    }

    const commentId = uuidv4();

    // Create comment
    await db.insert(fanPagePostComments).values({
      id: commentId,
      postId,
      userId: fanPageId ? null : session.user.id, // null if commenting as page
      fanPageId,
      content: validatedData.content,
      parentId: validatedData.parentId || null,
      likeCount: 0,
    });

    // Update post comment count
    await db
      .update(fanPagePosts)
      .set({
        commentCount: post[0].commentCount + 1,
      })
      .where(eq(fanPagePosts.id, postId));

    // Get the created comment with user/page info
    const newComment = await db
      .select({
        id: fanPagePostComments.id,
        content: fanPagePostComments.content,
        parentId: fanPagePostComments.parentId,
        likeCount: fanPagePostComments.likeCount,
        createdAt: fanPagePostComments.createdAt,
        user: {
          id: users.id,
          name: users.name,
          username: users.username,
          image: users.image,
        },
        fanPage: {
          id: fanPages.id,
          name: fanPages.name,
          username: fanPages.username,
          profileImage: fanPages.profileImage,
          isVerified: fanPages.isVerified,
        },
      })
      .from(fanPagePostComments)
      .leftJoin(users, eq(fanPagePostComments.userId, users.id))
      .leftJoin(fanPages, eq(fanPagePostComments.fanPageId, fanPages.id))
      .where(eq(fanPagePostComments.id, commentId))
      .limit(1);

    return NextResponse.json(newComment[0], { status: 201 });

  } catch (error) {
    console.error("Error creating comment:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create comment" },
      { status: 500 }
    );
  }
}
