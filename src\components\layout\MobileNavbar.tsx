"use client";

import { useState, Fragment, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import { Menu, Transition } from "@headlessui/react";
import {
  HomeIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon,
  BellIcon,
  UserCircleIcon,
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  ChevronDownIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  BanknotesIcon,
  CreditCardIcon,
  CommandLineIcon,
  UsersIcon,
  ShieldCheckIcon,
  QuestionMarkCircleIcon,
  HeartIcon,
  PlusIcon,
  BookmarkIcon,
  ClockIcon,
  CalendarIcon,
  PhotoIcon,
  VideoCameraIcon,
  BuildingStorefrontIcon,
  NewspaperIcon,
  GlobeAltIcon,
  DocumentTextIcon
} from "@heroicons/react/24/outline";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { SearchSuggestions } from "@/components/search/SearchSuggestions";
import { useNotificationCount } from "@/hooks/useNotificationCount";
import { haptic } from "@/utils/haptics";
import { useMobileView } from "@/hooks/useClientSide";
import { useWallet } from "@/contexts/WalletContext";

export function MobileNavbar() {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session } = useSession();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [username, setUsername] = useState("");
  const [searchInputRef, setSearchInputRef] = useState<HTMLInputElement | null>(null);

  const { notificationCounts } = useNotificationCount();
  const isMobile = useMobileView(640); // Only show on mobile devices
  const { balance, loading: walletLoading } = useWallet();

  // Helper function to format balance
  const formatBalance = (amount: string | number | undefined): string => {
    if (amount === undefined || amount === null) return "0.00";
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return isNaN(numAmount) ? "0.00" : numAmount.toFixed(2);
  };

  // Helper function to get color classes
  const getColorClasses = (color: string, isActive: boolean) => {
    const colorMap = {
      blue: {
        active: "bg-gradient-to-r from-blue-50 to-blue-100 text-blue-600 shadow-sm border border-blue-200",
        icon: "bg-blue-100 text-blue-600"
      },
      green: {
        active: "bg-gradient-to-r from-green-50 to-green-100 text-green-600 shadow-sm border border-green-200",
        icon: "bg-green-100 text-green-600"
      },
      yellow: {
        active: "bg-gradient-to-r from-yellow-50 to-yellow-100 text-yellow-600 shadow-sm border border-yellow-200",
        icon: "bg-yellow-100 text-yellow-600"
      },
      purple: {
        active: "bg-gradient-to-r from-purple-50 to-purple-100 text-purple-600 shadow-sm border border-purple-200",
        icon: "bg-purple-100 text-purple-600"
      },
      indigo: {
        active: "bg-gradient-to-r from-indigo-50 to-indigo-100 text-indigo-600 shadow-sm border border-indigo-200",
        icon: "bg-indigo-100 text-indigo-600"
      },
      pink: {
        active: "bg-gradient-to-r from-pink-50 to-pink-100 text-pink-600 shadow-sm border border-pink-200",
        icon: "bg-pink-100 text-pink-600"
      },
      orange: {
        active: "bg-gradient-to-r from-orange-50 to-orange-100 text-orange-600 shadow-sm border border-orange-200",
        icon: "bg-orange-100 text-orange-600"
      },
      emerald: {
        active: "bg-gradient-to-r from-emerald-50 to-emerald-100 text-emerald-600 shadow-sm border border-emerald-200",
        icon: "bg-emerald-100 text-emerald-600"
      },
      teal: {
        active: "bg-gradient-to-r from-teal-50 to-teal-100 text-teal-600 shadow-sm border border-teal-200",
        icon: "bg-teal-100 text-teal-600"
      },
      rose: {
        active: "bg-gradient-to-r from-rose-50 to-rose-100 text-rose-600 shadow-sm border border-rose-200",
        icon: "bg-rose-100 text-rose-600"
      },
      violet: {
        active: "bg-gradient-to-r from-violet-50 to-violet-100 text-violet-600 shadow-sm border border-violet-200",
        icon: "bg-violet-100 text-violet-600"
      }
    };

    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  // Main navigation items from left sidebar
  const mainNavigation = [
    { name: "News Feed", href: "/", icon: HomeIcon, color: "blue" },
    { name: "Connections", href: "/connection", icon: UsersIcon, color: "green" },
    { name: "Saved", href: "/saved", icon: BookmarkIcon, color: "yellow" },
    { name: "Memories", href: "/memories", icon: ClockIcon, color: "purple" },
    { name: "Groups", href: "/groups", icon: UserGroupIcon, color: "indigo" },
    { name: "Events", href: "/events", icon: CalendarIcon, color: "pink" },
    { name: "Pages", href: "/pages", icon: NewspaperIcon, color: "orange" },
    { name: "Marketplace", href: "/marketplace", icon: BuildingStorefrontIcon, color: "emerald" },
    { name: "Blogs", href: "/blogs", icon: DocumentTextIcon, color: "teal" },
    { name: "Photos", href: "/photos", icon: PhotoIcon, color: "rose" },
    { name: "Videos", href: "/videos", icon: VideoCameraIcon, color: "violet" },
  ];

  // Wallet navigation items
  const walletNavigation = [
    {
      name: walletLoading ? "Wallet (...)" : `Wallet ($${formatBalance(balance?.generalBalance)})`,
      href: "/wallet",
      icon: BanknotesIcon,
      color: "green"
    },
    {
      name: walletLoading ? "Earning World (...)" : `Earning World ($${formatBalance(balance?.earningBalance)})`,
      href: "/earning-world",
      icon: GlobeAltIcon,
      color: "blue"
    },
  ];



  useEffect(() => {
    if (session?.user?.username) {
      setUsername(session.user.username);
    }
  }, [session]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery("");
      setShowSuggestions(false);
      setMobileMenuOpen(false);
    }
  };

  const handleSignOut = async () => {
    haptic.medium();
    await signOut({ callbackUrl: "/" });
  };

  const handleMenuToggle = () => {
    haptic.light();
    setMobileMenuOpen(!mobileMenuOpen);
  };



  // Don't render if not on mobile
  if (!isMobile) return null;

  return (
    <>
      {/* Mobile Top Navbar */}
      <header className="bg-gradient-to-r from-white via-blue-50/30 to-white backdrop-blur-md shadow-sm border-b border-blue-100/50 fixed top-0 left-0 right-0 z-40 sm:hidden">
        <nav className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center group transition-all duration-300 hover:scale-105">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="HIFNF Logo"
                  width={80}
                  height={26}
                  priority
                  className="h-6 w-auto transition-all duration-300 group-hover:brightness-110"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
              </div>
            </Link>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-xs mx-4 relative">
            <form onSubmit={handleSearch} className="relative">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={setSearchInputRef}
                  type="text"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setShowSuggestions(true)}
                  onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                  className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-full bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              {showSuggestions && searchQuery && (
                <div className="absolute top-full left-0 right-0 mt-1 z-50">
                  <SearchSuggestions
                    query={searchQuery}
                    onSelect={(suggestion) => {
                      setSearchQuery(suggestion);
                      setShowSuggestions(false);
                      router.push(`/search?q=${encodeURIComponent(suggestion)}`);
                    }}
                    onClose={() => setShowSuggestions(false)}
                  />
                </div>
              )}
            </form>
          </div>

          {/* Menu Button with Notification Badge */}
          <div className="relative">
            <button
              type="button"
              className="p-2 rounded-lg text-gray-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 active:scale-95"
              onClick={handleMenuToggle}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            {(notificationCounts?.unread || 0) > 0 && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            )}
          </div>
        </nav>
      </header>

      {/* Mobile Slide-out Menu */}
      <Transition show={mobileMenuOpen} as={Fragment}>
        <div className="fixed inset-0 z-50 sm:hidden">
          {/* Backdrop */}
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setMobileMenuOpen(false)} />
          </Transition.Child>

          {/* Menu Panel */}
          <Transition.Child
            as={Fragment}
            enter="transition ease-in-out duration-300 transform"
            enterFrom="translate-x-full"
            enterTo="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leaveFrom="translate-x-0"
            leaveTo="translate-x-full"
          >
            <div className="fixed inset-y-0 right-0 z-50 w-full max-w-sm bg-gradient-to-b from-white to-blue-50/30 shadow-2xl overflow-y-auto scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-transparent">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-blue-100/50 bg-gradient-to-r from-blue-50/50 to-transparent">
                <Link
                  href={`/profile/${username}`}
                  className="flex items-center space-x-3 flex-1 rounded-xl p-2 -m-2 transition-all duration-200 hover:bg-blue-50/50 active:scale-95"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <div className="relative">
                    {session?.user?.image ? (
                      <Image
                        src={session.user.image}
                        alt="Profile"
                        width={40}
                        height={40}
                        className="rounded-full ring-2 ring-blue-100 transition-all duration-200 hover:ring-blue-300"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <UserCircleIcon className="h-6 w-6 text-white" />
                      </div>
                    )}
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-900">
                      {session?.user?.name || "User"}
                    </p>
                    <p className="text-xs text-blue-600 font-medium">@{username}</p>
                    <p className="text-xs text-gray-500 mt-0.5">View Profile</p>
                  </div>
                </Link>
                <button
                  type="button"
                  className="p-2 rounded-lg text-gray-400 hover:bg-gray-100 transition-colors"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>



              {/* Main Navigation */}
              <div className="p-4 border-t border-blue-100/50">
                <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  Main Navigation
                </h3>
                <div className="space-y-2">
                  {mainNavigation.map((item) => {
                    const isActive = pathname === item.href;
                    const colorClasses = getColorClasses(item.color, isActive);

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex items-center px-4 py-3 rounded-xl transition-all duration-200 active:scale-95",
                          isActive
                            ? colorClasses.active
                            : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:shadow-sm"
                        )}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <div className={cn(
                          "flex items-center justify-center h-8 w-8 rounded-full mr-3 transition-all duration-200",
                          isActive
                            ? colorClasses.icon
                            : "bg-gray-100 text-gray-600 group-hover:bg-gray-200"
                        )}>
                          <item.icon className="h-4 w-4" />
                        </div>
                        <span className="font-medium">{item.name}</span>
                      </Link>
                    );
                  })}
                </div>
              </div>

              {/* Wallet Navigation */}
              <div className="p-4 border-t border-blue-100/50">
                <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                  Wallet & Earnings
                </h3>
                <div className="space-y-2">
                  {walletNavigation.map((item) => {
                    const isActive = pathname === item.href;
                    const colorClasses = getColorClasses(item.color, isActive);

                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex items-center px-4 py-3 rounded-xl transition-all duration-200 active:scale-95",
                          isActive
                            ? colorClasses.active
                            : "text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:shadow-sm"
                        )}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <div className={cn(
                          "flex items-center justify-center h-8 w-8 rounded-full mr-3 transition-all duration-200",
                          isActive
                            ? colorClasses.icon
                            : "bg-gray-100 text-gray-600 group-hover:bg-gray-200"
                        )}>
                          <item.icon className="h-4 w-4" />
                        </div>
                        <span className="font-medium text-sm">{item.name}</span>
                      </Link>
                    );
                  })}
                </div>
              </div>

              {/* Account Section */}
              <div className="p-4 border-t border-blue-100/50 bg-gradient-to-r from-gray-50/50 to-transparent">
                <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                  Account
                </h3>
                <div className="space-y-2">
                  <Link
                    href={`/profile/${username}`}
                    className="flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 hover:text-purple-600 transition-all duration-200 active:scale-95 hover:shadow-sm"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <UserCircleIcon className="h-5 w-5 mr-3 transition-transform duration-200 hover:scale-110" />
                    <span className="font-medium">My Profile</span>
                  </Link>

                  <Link
                    href="/settings"
                    className="flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 transition-all duration-200 active:scale-95 hover:shadow-sm"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <Cog6ToothIcon className="h-5 w-5 mr-3 transition-transform duration-200 hover:scale-110" />
                    <span className="font-medium">Settings</span>
                  </Link>

                  <button
                    onClick={handleSignOut}
                    className="flex items-center w-full px-4 py-3 rounded-xl text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 transition-all duration-200 active:scale-95 hover:shadow-sm"
                  >
                    <ArrowRightOnRectangleIcon className="h-5 w-5 mr-3 transition-transform duration-200 hover:scale-110" />
                    <span className="font-medium">Sign Out</span>
                  </button>
                </div>
              </div>
            </div>
          </Transition.Child>
        </div>
      </Transition>
    </>
  );
}
